<?php

namespace App\Providers;

use App\Http\Middleware\VerifyShopifyCopy;
use App\Providers\Socialite\JudgeMeProvider;
use App\Services\FilamentNotificationService;
use App\Services\Shops\Shopify\CheckIfShopifyRequest;
use App\Services\Shops\Shopify\ShopManager;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;
use Laravel\Socialite\Facades\Socialite;
use Livewire\Livewire;
use Osiset\ShopifyApp\Contracts\Commands\Shop as ShopCommand;
use Osiset\ShopifyApp\Http\Middleware\IframeProtection;
use Osiset\ShopifyApp\Storage\Models\Plan as BasePlan;

class AppServiceProvider extends ServiceProvider
{

    public function register(): void
    {
        // Override the original ShopCommand with your custom ShopManager
        $this->app->bind(ShopCommand::class, ShopManager::class);

        // Register the custom notification
        $this->app->bind(Notification::class, FilamentNotificationService::class);

        // Register the custom Plan model
        $this->app->bind(BasePlan::class, App::class);
    }

    public function boot(): void
    {
        Socialite::extend('judgeme', function ($app) {
            $config = $app['config']['services.judgeme'];

            return Socialite::buildProvider(JudgeMeProvider::class, $config);
        });

        // Override the verify.shopify middleware to use our custom implementation
        $this->app['router']->aliasMiddleware('verify.shopify', VerifyShopifyCopy::class);

        if(CheckIfShopifyRequest::execute()) {
            Livewire::setUpdateRoute(function ($handle) {
                return Route::post('/livewire/update', $handle)
                    ->middleware([VerifyShopifyCopy::class, IframeProtection::class]);
            });
        }
    }
}
